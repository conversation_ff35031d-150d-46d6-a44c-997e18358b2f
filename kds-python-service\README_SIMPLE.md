# KDS AI Sequencing Service

A simple Python microservice that uses **OpenAI GPT** to intelligently sequence kitchen orders based on real-time parameters.

## 🤖 How It Works

1. **Get Orders**: Fetches pending orders from your PostgreSQL database
2. **AI Analysis**: Sends order data to OpenAI GPT with sequencing parameters
3. **Smart Sequencing**: AI considers station capacity, prep time, FIFO, priorities
4. **Return Results**: Returns optimally sequenced orders with reasoning

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- PostgreSQL database (with your existing orders)
- OpenAI API key

### Installation

1. **Setup:**
```bash
cd kds-python-service
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Configure:**
```bash
cp .env.example .env
# Edit .env:
# - DATABASE_URL=postgresql://user:pass@localhost:5432/your_db
# - OPENAI_API_KEY=your-openai-api-key
```

3. **Run:**
```bash
python start.py
```

## 🎯 Main API Endpoint

### POST `/api/orders/sequence`

**Request:**
```json
{
  "station_capacity": {
    "GRILL": 3,
    "FRYER": 2,
    "OVEN": 2,
    "STOVETOP": 2,
    "COLD_PREP": 4
  },
  "prioritize_fifo": true
}
```

**Response:**
```json
{
  "sequenced_orders": [
    {
      "sequence_position": 1,
      "order_id": 123,
      "order_number": "ORD-001",
      "assigned_station": "GRILL",
      "estimated_start_time": "2024-01-01T10:00:00",
      "estimated_completion_time": "2024-01-01T10:15:00",
      "reasoning": "VIP order with simple prep time"
    }
  ],
  "total_orders": 5,
  "parameters_used": {
    "station_capacity": {"GRILL": 3, "FRYER": 2},
    "prioritize_fifo": true
  }
}
```

## 🧠 AI Sequencing Logic

The AI considers these factors:

1. **Order Priority**: VIP > URGENT > HIGH > NORMAL > LOW
2. **Station Capacity**: Balances load across kitchen stations
3. **Preparation Time**: Optimizes cooking times and parallel processing
4. **FIFO**: Respects first-in-first-out when enabled
5. **Order Type**: Delivery, takeaway, dine-in considerations
6. **Delayed Orders**: Prioritizes orders that are running late

## 🔧 Integration with Node.js

Call from your Node.js backend:

```javascript
const response = await fetch('http://localhost:8000/api/orders/sequence', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    station_capacity: {
      GRILL: 3,
      FRYER: 2,
      OVEN: 2
    },
    prioritize_fifo: true
  })
});

const sequencedOrders = await response.json();
console.log('AI sequenced orders:', sequencedOrders);
```

## 📊 Station Types

The system recognizes these kitchen stations:
- **GRILL**: Grilled items
- **FRYER**: Fried items
- **OVEN**: Baked/roasted items
- **STOVETOP**: Pan-cooked items
- **COLD_PREP**: Salads, cold items
- **ASSEMBLY**: Final assembly/plating

## 🔄 Streaming Orders

For streaming orders, call the API whenever:
- New orders arrive
- Station capacity changes
- Orders are completed
- Priority changes occur

The AI will re-sequence based on current state.

## 🛠️ Environment Variables

```env
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/kds_db

# OpenAI
OPENAI_API_KEY=your-openai-api-key-here

# Optional
DEBUG=false
LOG_LEVEL=INFO
```

## 📝 Example Usage

```bash
# Test the API
curl -X POST "http://localhost:8000/api/orders/sequence" \
  -H "Content-Type: application/json" \
  -d '{
    "station_capacity": {"GRILL": 3, "FRYER": 2},
    "prioritize_fifo": true
  }'
```

## 🚨 Fallback

If OpenAI API fails, the system automatically falls back to a simple priority-based FIFO sequence to ensure continuous operation.

## 📋 Health Check

- `GET /health/` - Service health status
- `GET /` - Basic service info

That's it! Simple AI-powered order sequencing for your KDS system.
