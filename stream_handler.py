from fastapi import FastAP<PERSON>, WebSocket, HTTPException
from pydantic import BaseModel
from typing import List, Dict
import asyncio
import json
from datetime import datetime
from queue_manager import queue_manager, OrderStatus

app = FastAPI(title="KDS Order Stream Handler")

class IncomingOrder(BaseModel):
    order_number: str
    table_id: str
    customer_name: str
    order_type: str
    items: List[Dict]

class StatusUpdate(BaseModel):
    item_id: int
    status: str

# WebSocket for real-time updates
@app.websocket("/ws/kitchen/{kitchen_id}")
async def kitchen_websocket(websocket: WebSocket, kitchen_id: int):
    await websocket.accept()
    
    try:
        while True:
            # Send real-time queue updates every 2 seconds
            queue_data = await queue_manager.get_real_time_queue(kitchen_id)
            await websocket.send_json(queue_data)
            await asyncio.sleep(2)
            
    except Exception as e:
        print(f"WebSocket error: {e}")
    finally:
        await websocket.close()

# Webhook for incoming orders (from POS system)
@app.post("/webhook/order")
async def receive_order(order: IncomingOrder):
    """Receive streaming order from POS system"""
    try:
        # Convert to internal format
        order_data = {
            'order_number': order.order_number,
            'table_id': order.table_id,
            'customer_name': order.customer_name,
            'order_type': order.order_type,
            'items': []
        }
        
        # Process items and assign to kitchens
        for item in order.items:
            # Map items to kitchen stations
            kitchen_id = _get_kitchen_for_item(item['name'])
            
            order_data['items'].append({
                'item_name': item['name'],
                'quantity': item['quantity'],
                'kitchen_id': kitchen_id,
                'est_cook_time_sec': item.get('cook_time', 300),  # Default 5 min
                'priority': item.get('priority', 0)
            })
        
        # Process through queue manager
        await queue_manager.handle_incoming_order(order_data)
        
        return {"status": "success", "order_number": order.order_number}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to process order: {e}")

# Status updates from kitchen staff
@app.patch("/api/items/{item_id}/status")
async def update_item_status(item_id: int, status_update: StatusUpdate):
    """Update item status from kitchen display"""
    try:
        new_status = OrderStatus(status_update.status)
        await queue_manager.update_item_status(item_id, new_status)
        
        return {"status": "success", "item_id": item_id, "new_status": status_update.status}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update status: {e}")

# Get current queue for display
@app.get("/api/kitchen/{kitchen_id}/queue")
async def get_kitchen_display(kitchen_id: int):
    """Get current kitchen queue for KDS display"""
    try:
        return await queue_manager.get_real_time_queue(kitchen_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get queue: {e}")

# Manual resequencing trigger
@app.post("/api/kitchen/{kitchen_id}/resequence")
async def manual_resequence(kitchen_id: int):
    """Manual trigger for AI resequencing"""
    try:
        await queue_manager._trigger_sequencing(kitchen_id)
        return {"status": "success", "message": f"Kitchen {kitchen_id} resequenced"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to resequence: {e}")

def _get_kitchen_for_item(item_name: str) -> int:
    """Map menu items to kitchen stations"""
    item_kitchen_map = {
        'steak': 1, 'burger': 1, 'chicken': 1,  # Grill
        'fries': 2, 'wings': 2, 'onion rings': 2,  # Fryer
        'salad': 3, 'dessert': 3, 'cake': 3,  # Cold
        'beer': 4, 'cocktail': 4, 'soda': 4   # Bar
    }
    
    for key, kitchen_id in item_kitchen_map.items():
        if key.lower() in item_name.lower():
            return kitchen_id
    
    return 1  # Default to grill

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)