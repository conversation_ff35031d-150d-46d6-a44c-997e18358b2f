#!/usr/bin/env python3
"""
Startup script for KDS Python service.
"""
import uvicorn
import sys
import os
from pathlib import Path

# Add the app directory to Python path
app_dir = Path(__file__).parent
sys.path.insert(0, str(app_dir))

from app.core.config import settings

if __name__ == "__main__":
    # Configure uvicorn
    config = {
        "app": "app.main:app",
        "host": "0.0.0.0",
        "port": 8000,
        "reload": settings.debug,
        "log_level": settings.log_level.lower(),
        "access_log": True,
        "use_colors": True,
    }
    
    print(f"Starting {settings.app_name} v{settings.app_version}")
    print(f"Debug mode: {settings.debug}")
    print(f"Log level: {settings.log_level}")
    print(f"Database URL: {settings.database_url}")
    print(f"Server will be available at: http://localhost:8000")
    print(f"API documentation: http://localhost:8000/docs")
    print("-" * 50)
    
    # Start the server
    uvicorn.run(**config)
