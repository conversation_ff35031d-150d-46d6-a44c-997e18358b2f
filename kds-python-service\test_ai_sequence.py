#!/usr/bin/env python3
"""
Simple test script to demonstrate AI sequencing.
"""
import requests
import json
import time

# API endpoint
API_URL = "http://localhost:8000/api/orders/sequence"

def test_ai_sequencing():
    """Test the AI sequencing endpoint."""
    
    print("🤖 Testing KDS AI Sequencing Service")
    print("=" * 50)
    
    # Test data - different scenarios
    test_scenarios = [
        {
            "name": "Balanced Load",
            "data": {
                "station_capacity": {
                    "GRILL": 3,
                    "FRYER": 2,
                    "OVEN": 2,
                    "STOVETOP": 2,
                    "COLD_PREP": 4
                },
                "prioritize_fifo": True
            }
        },
        {
            "name": "High Grill Capacity",
            "data": {
                "station_capacity": {
                    "GRILL": 5,
                    "FRYER": 1,
                    "OVEN": 1,
                    "STOVETOP": 1,
                    "COLD_PREP": 2
                },
                "prioritize_fifo": False
            }
        },
        {
            "name": "FIFO Disabled",
            "data": {
                "station_capacity": {
                    "GRILL": 2,
                    "FRYER": 2,
                    "OVEN": 2,
                    "STOVETOP": 2,
                    "COLD_PREP": 3
                },
                "prioritize_fifo": False
            }
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📋 Test {i}: {scenario['name']}")
        print("-" * 30)
        
        try:
            # Make API request
            response = requests.post(
                API_URL,
                json=scenario['data'],
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ Success! Sequenced {result['total_orders']} orders")
                print(f"Parameters: {json.dumps(result['parameters_used'], indent=2)}")
                
                if result['sequenced_orders']:
                    print("\n🎯 Top 3 Sequenced Orders:")
                    for order in result['sequenced_orders'][:3]:
                        print(f"  {order['sequence_position']}. {order['order_number']} "
                              f"-> {order['assigned_station']} "
                              f"({order['reasoning']})")
                else:
                    print("ℹ️  No orders to sequence")
                    
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
        
        # Small delay between tests
        if i < len(test_scenarios):
            time.sleep(1)
    
    print("\n" + "=" * 50)
    print("🏁 Testing complete!")


def test_health_check():
    """Test the health check endpoint."""
    print("\n🏥 Testing Health Check...")
    
    try:
        response = requests.get("http://localhost:8000/health/", timeout=10)
        if response.status_code == 200:
            health = response.json()
            print(f"✅ Service is {health['status']}")
            print(f"Database connected: {health['database_connected']}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check error: {e}")


if __name__ == "__main__":
    print("🚀 Starting KDS AI Sequencing Tests")
    print("Make sure the service is running on http://localhost:8000")
    print()
    
    # Test health first
    test_health_check()
    
    # Test AI sequencing
    test_ai_sequencing()
    
    print("\n💡 Tips:")
    print("- Make sure you have orders in your database")
    print("- Set OPENAI_API_KEY in your .env file")
    print("- Check logs if sequencing fails")
    print("- API docs available at: http://localhost:8000/docs")
