# KDS Queue Management Architecture

## 🔄 Complete Data Flow

```
POS System → Stream Handler → Queue Manager → AI Sequencer → Database + Redis → KDS Display
```

### 1. **Order Ingestion Layer**
```
📱 POS System sends order via webhook
↓
🌐 stream_handler.py receives HTTP POST
↓
📋 Validates and maps items to kitchens
↓
🚀 Triggers queue_manager.handle_incoming_order()
```

### 2. **Queue Management Layer**
```
📦 Queue Manager receives order
↓
💾 Stores in PostgreSQL (persistent)
↓
⚡ Adds to Redis queue (real-time)
↓
🤖 Triggers AI sequencing
↓
📊 Updates sequence in DB + Redis cache
```

### 3. **AI Sequencing Layer**
```
🧠 AI Sequencer fetches active orders
↓
🔢 Calculates priority scores
↓
📈 Assigns sequence numbers
↓
⚙️ Manages kitchen capacity
↓
💾 Updates database with decisions
```

### 4. **Display Layer**
```
🖥️ KDS Display requests queue
↓
⚡ Checks Redis cache first
↓
💾 Falls back to database
↓
📺 Shows real-time sequenced queue
```

## 🗄️ Data Storage Strategy

### **PostgreSQL (Persistent Storage)**
- ✅ All orders and items
- ✅ Sequence numbers
- ✅ Status history
- ✅ Analytics data

### **Redis (Real-time Cache)**
- ⚡ Active kitchen queues
- ⚡ Sequence cache (5 min TTL)
- ⚡ WebSocket session data
- ⚡ Temporary processing queues

### **In-Memory (Application State)**
- 🔄 Active WebSocket connections
- 🔄 Kitchen capacity counters
- 🔄 AI model state

## 🚀 Key Benefits

### **Streaming Data Handling**
- Orders processed immediately as they arrive
- No polling needed - event-driven architecture
- Real-time updates via WebSocket

### **Queue Management**
- Redis provides fast queue operations
- PostgreSQL ensures data persistence
- AI sequencing runs on-demand

### **Scalability**
- Each kitchen has independent queue
- Redis handles high-frequency updates
- Database handles complex queries

### **Fault Tolerance**
- Redis failure → Falls back to database
- Database failure → Uses Redis cache
- AI failure → Falls back to FIFO

## 📊 Performance Characteristics

- **Order Processing**: < 100ms
- **AI Sequencing**: < 500ms
- **Queue Updates**: < 50ms
- **WebSocket Updates**: Real-time (2s interval)

## 🔧 Configuration

```python
# Queue settings
REDIS_TTL = 300  # 5 minutes
SEQUENCE_INTERVAL = 10  # seconds
MAX_QUEUE_SIZE = 50  # per kitchen

# AI settings
AI_TRIGGER_THRESHOLD = 3  # new orders
CAPACITY_LIMITS = {
    'grill': 3,
    'fryer': 2,
    'cold': 4,
    'bar': 2
}
```