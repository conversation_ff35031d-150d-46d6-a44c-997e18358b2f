"""
Test configuration and fixtures.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import get_db, Base
from app.models.order import Order, OrderStatus, OrderType, OrderPriority
from app.models.order_item import OrderItem
from app.models.menu_item import MenuItem

# Test database URL (in-memory SQLite)
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="session")
def db_engine():
    """Create test database engine."""
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def db_session(db_engine):
    """Create test database session."""
    connection = db_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def sample_menu_items(db_session):
    """Create sample menu items for testing."""
    items = [
        MenuItem(
            id=1,
            name="Grilled Chicken",
            category="MAIN_COURSE",
            price=15.99,
            prep_time=12,
            station_required="GRILL",
            complexity_level=2
        ),
        MenuItem(
            id=2,
            name="French Fries",
            category="SIDE",
            price=4.99,
            prep_time=5,
            station_required="FRYER",
            complexity_level=1
        ),
        MenuItem(
            id=3,
            name="Caesar Salad",
            category="APPETIZER",
            price=8.99,
            prep_time=3,
            station_required="COLD_PREP",
            complexity_level=1
        )
    ]
    
    for item in items:
        db_session.add(item)
    db_session.commit()
    
    return items


@pytest.fixture
def sample_orders(db_session, sample_menu_items):
    """Create sample orders for testing."""
    orders = [
        Order(
            id=1,
            order_number="ORD-001",
            customer_name="John Doe",
            table_number=5,
            order_type=OrderType.DINE_IN,
            status=OrderStatus.CONFIRMED,
            priority=OrderPriority.NORMAL,
            total_amount=20.98,
            estimated_prep_time=15
        ),
        Order(
            id=2,
            order_number="ORD-002",
            customer_name="Jane Smith",
            order_type=OrderType.DELIVERY,
            status=OrderStatus.CONFIRMED,
            priority=OrderPriority.VIP,
            total_amount=15.99,
            estimated_prep_time=12,
            is_delayed=True
        )
    ]
    
    for order in orders:
        db_session.add(order)
    db_session.commit()
    
    # Add order items
    order_items = [
        OrderItem(
            order_id=1,
            menu_item_id=1,
            quantity=1,
            unit_price=15.99,
            total_price=15.99,
            prep_time=12
        ),
        OrderItem(
            order_id=1,
            menu_item_id=2,
            quantity=1,
            unit_price=4.99,
            total_price=4.99,
            prep_time=5
        ),
        OrderItem(
            order_id=2,
            menu_item_id=1,
            quantity=1,
            unit_price=15.99,
            total_price=15.99,
            prep_time=12
        )
    ]
    
    for item in order_items:
        db_session.add(item)
    db_session.commit()
    
    return orders
