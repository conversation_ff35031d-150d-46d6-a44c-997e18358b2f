version: '3.8'

services:
  kds-python-service:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/kds_db
      - NODEJS_API_BASE_URL=http://nodejs-backend:3000/api
      - DEBUG=false
    depends_on:
      - postgres
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - kds-network

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=kds_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - kds-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - kds-network

volumes:
  postgres_data:
  redis_data:

networks:
  kds-network:
    driver: bridge
