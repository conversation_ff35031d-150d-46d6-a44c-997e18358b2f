"""
Simple KDS AI Sequencing Service - AI-powered order sequencing only.
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api import orders, health

# Create FastAPI application
app = FastAPI(
    title="KDS AI Sequencing Service",
    version="1.0.0",
    description="Simple AI-powered order sequencing for KDS"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health.router, prefix="/health", tags=["Health"])
app.include_router(orders.router, prefix="/api/orders", tags=["Orders"])


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "service": "KDS AI Sequencing Service",
        "version": "1.0.0",
        "status": "running",
        "description": "AI-powered order sequencing using OpenAI"
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
