"""
AI Agent for KDS Order Sequencing using OpenAI.
"""
import json
import logging
from typing import List, Dict
from datetime import datetime
import openai
import os

# Initialize OpenAI
openai.api_key = os.getenv("OPENAI_API_KEY")

logger = logging.getLogger(__name__)


class KDSSequencingAgent:
    """
    AI Agent for intelligent order sequencing in Kitchen Display System.
    """
    
    def __init__(self):
        self.model = "gpt-4"
        self.temperature = 0.3
        self.max_tokens = 2000
    
    async def sequence_orders(self, orders: List[Dict], station_capacity: Dict[str, int], prioritize_fifo: bool) -> List[Dict]:
        """
        Use OpenAI to sequence orders based on parameters.
        """
        try:
            # Create the AI prompt
            prompt = self._create_sequencing_prompt(orders, station_capacity, prioritize_fifo)
            
            # Call OpenAI API
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a kitchen sequencing AI. Return only JSON responses."},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            # Parse AI response
            ai_response = response.choices[0].message.content.strip()
            result = json.loads(ai_response)
            
            return result.get("sequenced_orders", [])
            
        except Exception as e:
            logger.error(f"AI sequencing failed: {e}")
            # Fallback to simple FIFO if AI fails
            return self._create_fallback_sequence(orders)
    
    def _create_sequencing_prompt(self, orders: List[Dict], station_capacity: Dict[str, int], prioritize_fifo: bool) -> str:
        """
        Create the AI prompt for order sequencing.
        """
        prompt = f"""
You are a Kitchen Display System AI that sequences orders for optimal kitchen efficiency.

PARAMETERS:
- Station Capacity: {json.dumps(station_capacity)}
- Prioritize FIFO (First In, First Out): {prioritize_fifo}

ORDERS TO SEQUENCE:
{json.dumps(orders, indent=2)}

SEQUENCING RULES:
1. VIP and URGENT orders get highest priority
2. Consider station capacity - don't overload stations
3. Balance preparation times across stations
4. If FIFO is enabled, older orders generally come first (unless VIP/URGENT)
5. Group orders that use same stations when possible
6. Consider order complexity and prep time

RESPONSE FORMAT (JSON only):
{{
  "sequenced_orders": [
    {{
      "sequence_position": 1,
      "order_id": 123,
      "order_number": "ORD-001",
      "assigned_station": "GRILL",
      "estimated_start_time": "2024-01-01T10:00:00",
      "estimated_completion_time": "2024-01-01T10:15:00",
      "reasoning": "VIP order with simple prep time"
    }}
  ]
}}

Return ONLY the JSON response, no other text.
"""
        return prompt
    
    def _create_fallback_sequence(self, orders: List[Dict]) -> List[Dict]:
        """
        Create a simple fallback sequence if AI fails.
        """
        sequenced = []
        current_time = datetime.now()
        
        # Sort by priority and creation time
        sorted_orders = sorted(orders, key=lambda x: (
            0 if x["priority"] == "VIP" else 1 if x["priority"] == "URGENT" else 2,
            x["created_at"]
        ))
        
        for i, order in enumerate(sorted_orders):
            # Simple station assignment based on first item
            station = "GENERAL"
            if order["items"]:
                station = order["items"][0].get("station_required", "GENERAL")
            
            # Estimate timing
            prep_time = order["estimated_prep_time"]
            start_time = current_time.replace(microsecond=0)
            completion_time = start_time.replace(second=start_time.second + prep_time * 60)
            
            sequenced.append({
                "sequence_position": i + 1,
                "order_id": order["id"],
                "order_number": order["order_number"],
                "assigned_station": station,
                "estimated_start_time": start_time.isoformat(),
                "estimated_completion_time": completion_time.isoformat(),
                "reasoning": f"Fallback sequence - {order['priority']} priority"
            })
        
        return sequenced
    
    def update_model_settings(self, model: str = None, temperature: float = None, max_tokens: int = None):
        """
        Update AI model settings.
        """
        if model:
            self.model = model
        if temperature is not None:
            self.temperature = temperature
        if max_tokens:
            self.max_tokens = max_tokens
        
        logger.info(f"AI model settings updated: model={self.model}, temperature={self.temperature}, max_tokens={self.max_tokens}")
    
    def get_model_info(self) -> Dict:
        """
        Get current AI model configuration.
        """
        return {
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "api_key_configured": bool(openai.api_key)
        }


# Global AI agent instance
ai_agent = KDSSequencingAgent()
