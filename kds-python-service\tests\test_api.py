"""
Tests for API endpoints.
"""
import pytest
from fastapi.testclient import TestClient


class TestHealthAPI:
    """Test health check endpoints."""
    
    def test_health_check(self, client: TestClient):
        """Test main health check endpoint."""
        response = client.get("/health/")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
        assert "database_connected" in data
        assert "ai_agent_status" in data
    
    def test_readiness_check(self, client: TestClient):
        """Test readiness endpoint."""
        response = client.get("/health/ready")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
    
    def test_liveness_check(self, client: TestClient):
        """Test liveness endpoint."""
        response = client.get("/health/live")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "alive"
        assert "timestamp" in data


class TestOrdersAPI:
    """Test orders API endpoints."""
    
    def test_get_orders(self, client: TestClient, sample_orders):
        """Test get orders endpoint."""
        response = client.get("/api/orders/")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_pending_orders(self, client: TestClient, sample_orders):
        """Test get pending orders endpoint."""
        response = client.get("/api/orders/pending")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_order_by_id(self, client: TestClient, sample_orders):
        """Test get specific order endpoint."""
        order_id = sample_orders[0].id
        response = client.get(f"/api/orders/{order_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == order_id
        assert "order_number" in data
        assert "status" in data
    
    def test_get_nonexistent_order(self, client: TestClient):
        """Test get non-existent order."""
        response = client.get("/api/orders/999")
        assert response.status_code == 404
    
    def test_update_order_status(self, client: TestClient, sample_orders):
        """Test update order status endpoint."""
        order_id = sample_orders[0].id
        response = client.patch(
            f"/api/orders/{order_id}/status",
            params={"status": "PREPARING"}
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["order_id"] == order_id
        assert data["status"] == "PREPARING"
    
    def test_get_order_stats(self, client: TestClient, sample_orders):
        """Test order statistics endpoint."""
        response = client.get("/api/orders/stats/summary")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, dict)
        # Should have various count fields
        assert any(key.endswith("_orders") for key in data.keys())


class TestSequenceAPI:
    """Test sequencing API endpoints."""
    
    def test_sequence_orders(self, client: TestClient, sample_orders):
        """Test order sequencing endpoint."""
        response = client.post(
            "/api/sequence/orders",
            json={
                "max_orders": 10,
                "force_refresh": True
            }
        )
        assert response.status_code == 200
        
        data = response.json()
        assert "sequenced_orders" in data
        assert "total_orders" in data
        assert "estimated_total_time" in data
        assert "stations_utilization" in data
        assert "generated_at" in data
    
    def test_sequence_specific_orders(self, client: TestClient, sample_orders):
        """Test sequencing specific orders."""
        order_ids = [order.id for order in sample_orders]
        
        response = client.post(
            "/api/sequence/orders",
            json={
                "order_ids": order_ids,
                "force_refresh": True
            }
        )
        assert response.status_code == 200
        
        data = response.json()
        assert len(data["sequenced_orders"]) <= len(order_ids)
    
    def test_get_current_sequence(self, client: TestClient, sample_orders):
        """Test get current sequence endpoint."""
        response = client.get("/api/sequence/current")
        assert response.status_code == 200
        
        data = response.json()
        assert "sequenced_orders" in data
        assert "total_orders" in data
        assert isinstance(data["sequenced_orders"], list)
    
    def test_refresh_sequence(self, client: TestClient, sample_orders):
        """Test sequence refresh endpoint."""
        response = client.post("/api/sequence/refresh")
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
        assert "orders_processed" in data
        assert "timestamp" in data
    
    def test_get_sequencing_metrics(self, client: TestClient, sample_orders):
        """Test sequencing metrics endpoint."""
        response = client.get("/api/sequence/metrics")
        assert response.status_code == 200
        
        data = response.json()
        assert "order_metrics" in data
        assert "station_workload" in data
        assert "delayed_orders_count" in data
        assert "stations_utilization" in data
        assert "timestamp" in data


class TestRootEndpoint:
    """Test root endpoint."""
    
    def test_root_endpoint(self, client: TestClient):
        """Test root endpoint returns service info."""
        response = client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        assert "service" in data
        assert "version" in data
        assert "status" in data
        assert "timestamp" in data
        assert data["status"] == "running"
