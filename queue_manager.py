import asyncio
import json
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum
import psycopg2
from psycopg2.extras import RealDictCursor
import redis
import logging

class OrderStatus(Enum):
    NEW = "New"
    QUEUED = "Queued"
    PREPARING = "Preparing"
    READY = "Ready"
    SERVED = "Served"

@dataclass
class OrderItem:
    id: int
    order_number: str
    table_id: str
    item_name: str
    kitchen_id: int
    quantity: int
    est_cook_time_sec: int
    priority: int
    status: OrderStatus
    created_at: datetime
    sequence_no: Optional[int] = None

class QueueManager:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        self.db_conn = self._get_db()
        self.kitchen_queues = {}  # In-memory active queues
        self.logger = logging.getLogger(__name__)
        
    def _get_db(self):
        return psycopg2.connect(
            host="localhost", database="kds", 
            user="postgres", password="password",
            cursor_factory=RealDictCursor
        )
    
    async def handle_incoming_order(self, order_data: Dict):
        """Handle streaming order data"""
        try:
            # 1. Store in database
            order_id = await self._store_order_in_db(order_data)
            
            # 2. Add to Redis queue for real-time processing
            await self._add_to_redis_queue(order_data, order_id)
            
            # 3. Trigger AI sequencing
            await self._trigger_sequencing(order_data['kitchen_id'])
            
            self.logger.info(f"Order {order_data['order_number']} processed")
            
        except Exception as e:
            self.logger.error(f"Failed to handle order: {e}")
    
    async def _store_order_in_db(self, order_data: Dict) -> int:
        """Store order in PostgreSQL"""
        with self.db_conn.cursor() as cur:
            # Insert order if not exists
            cur.execute("""
                INSERT INTO orders (order_number, table_id, customer_name)
                VALUES (%(order_number)s, %(table_id)s, %(customer_name)s)
                ON CONFLICT (order_number) DO NOTHING
                RETURNING id
            """, order_data)
            
            result = cur.fetchone()
            if result:
                order_id = result['id']
            else:
                # Get existing order_id
                cur.execute("SELECT id FROM orders WHERE order_number = %s", 
                           (order_data['order_number'],))
                order_id = cur.fetchone()['id']
            
            # Insert order items
            for item in order_data['items']:
                cur.execute("""
                    INSERT INTO order_items (order_id, kitchen_id, item_name, quantity, 
                                           est_cook_time_sec, priority, status)
                    VALUES (%s, %s, %s, %s, %s, %s, 'New')
                """, (order_id, item['kitchen_id'], item['item_name'], 
                     item['quantity'], item['est_cook_time_sec'], item.get('priority', 0)))
            
            self.db_conn.commit()
            return order_id
    
    async def _add_to_redis_queue(self, order_data: Dict, order_id: int):
        """Add to Redis for real-time queue management"""
        queue_key = f"kitchen_queue:{order_data['kitchen_id']}"
        
        queue_item = {
            'order_id': order_id,
            'order_number': order_data['order_number'],
            'timestamp': datetime.now().isoformat(),
            'items': order_data['items']
        }
        
        # Add to Redis list (FIFO queue)
        self.redis_client.lpush(queue_key, json.dumps(queue_item))
        
        # Set expiry (24 hours)
        self.redis_client.expire(queue_key, 86400)
    
    async def _trigger_sequencing(self, kitchen_id: int):
        """Trigger AI sequencing for kitchen"""
        from sequencer import AISequencer
        
        sequencer = AISequencer()
        
        # Get current queue from Redis + DB
        queue_items = await self._get_kitchen_queue(kitchen_id)
        
        if queue_items:
            # Run AI sequencing
            sequence = sequencer.sequence_kitchen(kitchen_id, queue_items)
            
            # Update database with new sequence
            await self._update_sequence_in_db(sequence)
            
            # Update Redis cache
            await self._update_redis_cache(kitchen_id, sequence)
    
    async def _get_kitchen_queue(self, kitchen_id: int) -> List[Dict]:
        """Get current kitchen queue from DB"""
        with self.db_conn.cursor() as cur:
            cur.execute("""
                SELECT oi.id, o.order_number, o.table_id, oi.item_name,
                       oi.quantity, oi.est_cook_time_sec, oi.created_at,
                       oi.priority, oi.status, oi.sequence_no
                FROM order_items oi
                JOIN orders o ON oi.order_id = o.id
                WHERE oi.kitchen_id = %s AND oi.status IN ('New', 'Preparing')
                ORDER BY oi.created_at
            """, (kitchen_id,))
            
            return cur.fetchall()
    
    async def _update_sequence_in_db(self, sequence: List[Dict]):
        """Update sequence numbers in database"""
        with self.db_conn.cursor() as cur:
            for item in sequence:
                cur.execute("""
                    UPDATE order_items 
                    SET sequence_no = %s, status = %s, updated_at = now()
                    WHERE id = %s
                """, (item['sequence_no'], item['status'], item['id']))
            
            self.db_conn.commit()
    
    async def _update_redis_cache(self, kitchen_id: int, sequence: List[Dict]):
        """Update Redis cache with sequenced data"""
        cache_key = f"kitchen_sequence:{kitchen_id}"
        
        sequence_data = {
            'sequence': sequence,
            'updated_at': datetime.now().isoformat(),
            'kitchen_id': kitchen_id
        }
        
        self.redis_client.setex(cache_key, 300, json.dumps(sequence_data))  # 5 min cache
    
    async def get_real_time_queue(self, kitchen_id: int) -> Dict:
        """Get real-time queue for display"""
        # Try Redis cache first
        cache_key = f"kitchen_sequence:{kitchen_id}"
        cached = self.redis_client.get(cache_key)
        
        if cached:
            return json.loads(cached)
        
        # Fallback to database
        queue_items = await self._get_kitchen_queue(kitchen_id)
        return {
            'sequence': queue_items,
            'updated_at': datetime.now().isoformat(),
            'kitchen_id': kitchen_id,
            'source': 'database'
        }
    
    async def update_item_status(self, item_id: int, new_status: OrderStatus):
        """Update item status and retrigger sequencing"""
        with self.db_conn.cursor() as cur:
            # Update status
            cur.execute("""
                UPDATE order_items 
                SET status = %s, updated_at = now()
                WHERE id = %s
                RETURNING kitchen_id
            """, (new_status.value, item_id))
            
            kitchen_id = cur.fetchone()['kitchen_id']
            self.db_conn.commit()
            
            # Retrigger sequencing if item completed
            if new_status in [OrderStatus.READY, OrderStatus.SERVED]:
                await self._trigger_sequencing(kitchen_id)

# Global queue manager instance
queue_manager = QueueManager()