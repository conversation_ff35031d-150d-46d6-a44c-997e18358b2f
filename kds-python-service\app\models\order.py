"""
Order model for KDS system.
"""
from sqlalchemy import Column, Integer, String, DateTime, Float, Boolean, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from enum import Enum as PyEnum
from typing import List, Optional

from app.core.database import Base


class OrderStatus(PyEnum):
    """Order status enumeration."""
    PENDING = "PENDING"
    CONFIRMED = "CONFIRMED"
    PREPARING = "PREPARING"
    READY = "READY"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"


class OrderType(PyEnum):
    """Order type enumeration."""
    DINE_IN = "DINE_IN"
    TAKEAWAY = "TAKEAWAY"
    DELIVERY = "DELIVERY"


class OrderPriority(PyEnum):
    """Order priority enumeration."""
    LOW = "LOW"
    NORMAL = "NORMAL"
    HIGH = "HIGH"
    VIP = "VIP"
    URGENT = "URGENT"


class Order(Base):
    """Order model."""
    
    __tablename__ = "orders"
    
    id = Column(Integer, primary_key=True, index=True)
    order_number = Column(String(50), unique=True, nullable=False, index=True)
    
    # Customer information
    customer_name = Column(String(100))
    customer_phone = Column(String(20))
    table_number = Column(Integer, nullable=True)
    
    # Order details
    order_type = Column(Enum(OrderType), nullable=False, default=OrderType.DINE_IN)
    status = Column(Enum(OrderStatus), nullable=False, default=OrderStatus.PENDING)
    priority = Column(Enum(OrderPriority), nullable=False, default=OrderPriority.NORMAL)
    
    # Timing
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    estimated_prep_time = Column(Integer, nullable=True)  # in minutes
    promised_time = Column(DateTime(timezone=True), nullable=True)
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Financial
    total_amount = Column(Float, nullable=False, default=0.0)
    
    # Special instructions
    special_instructions = Column(Text, nullable=True)
    
    # KDS specific fields
    sequence_position = Column(Integer, nullable=True)
    assigned_station = Column(String(50), nullable=True)
    is_delayed = Column(Boolean, default=False)
    delay_reason = Column(String(200), nullable=True)
    
    # Relationships
    order_items = relationship("OrderItem", back_populates="order", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Order(id={self.id}, order_number='{self.order_number}', status='{self.status}')>"
    
    @property
    def is_vip(self) -> bool:
        """Check if order is VIP priority."""
        return self.priority == OrderPriority.VIP
    
    @property
    def is_urgent(self) -> bool:
        """Check if order is urgent."""
        return self.priority in [OrderPriority.URGENT, OrderPriority.VIP]
    
    @property
    def total_prep_time(self) -> int:
        """Calculate total preparation time from order items."""
        if not self.order_items:
            return self.estimated_prep_time or 0
        return sum(item.prep_time or 0 for item in self.order_items)
    
    @property
    def minutes_since_created(self) -> int:
        """Get minutes since order was created."""
        if not self.created_at:
            return 0
        delta = datetime.utcnow() - self.created_at.replace(tzinfo=None)
        return int(delta.total_seconds() / 60)
