"""
Order sequencing API endpoints.
"""
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
import logging

from app.core.database import get_db
from app.core.schemas import (
    SequenceRequest, SequenceResponse, SequencedOrderSchema
)
from app.models.order import Order
from app.services.ai_sequencer import AISequencer
from app.services.order_service import OrderService

logger = logging.getLogger(__name__)
router = APIRouter()

# Global instances
ai_sequencer = AISequencer()
order_service = OrderService()


@router.post("/orders", response_model=SequenceResponse)
async def sequence_orders(
    request: SequenceRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Sequence orders using AI algorithm.
    """
    try:
        logger.info(f"Sequencing request: {request}")
        
        # Reset station capacities if force refresh
        if request.force_refresh:
            ai_sequencer.reset_station_capacities()
        
        # Get orders to sequence
        if request.order_ids:
            # Sequence specific orders
            orders = []
            for order_id in request.order_ids:
                order = db.query(Order).filter(Order.id == order_id).first()
                if order:
                    orders.append(order)
        else:
            # Get all orders for sequencing
            orders = order_service.get_orders_for_sequencing(
                db, 
                station_filter=request.station_filter,
                max_orders=request.max_orders or 50
            )
        
        if not orders:
            return SequenceResponse(
                sequenced_orders=[],
                total_orders=0,
                estimated_total_time=0,
                stations_utilization={},
                generated_at=datetime.utcnow()
            )
        
        # Run AI sequencing
        sequenced_orders = ai_sequencer.sequence_orders(orders, db)
        
        # Update database with new sequences
        background_tasks.add_task(
            order_service.update_order_sequences, db, sequenced_orders
        )
        
        # Notify Node.js backend
        background_tasks.add_task(
            order_service.notify_nodejs_backend,
            "orders_sequenced",
            {
                "total_orders": len(sequenced_orders),
                "station_filter": request.station_filter
            }
        )
        
        # Calculate response data
        total_time = sum(
            (seq_order["estimated_completion_time"] - seq_order["estimated_start_time"]).total_seconds() / 60
            for seq_order in sequenced_orders
        )
        
        stations_utilization = ai_sequencer.get_station_utilization()
        
        # Convert to response schema
        sequenced_order_schemas = []
        for seq_order in sequenced_orders:
            schema = SequencedOrderSchema(
                order=seq_order["order"],
                sequence_position=seq_order["sequence_position"],
                estimated_start_time=seq_order["estimated_start_time"],
                estimated_completion_time=seq_order["estimated_completion_time"],
                assigned_station=seq_order["assigned_station"],
                priority_score=seq_order["priority_score"],
                reasoning=seq_order["reasoning"]
            )
            sequenced_order_schemas.append(schema)
        
        response = SequenceResponse(
            sequenced_orders=sequenced_order_schemas,
            total_orders=len(sequenced_orders),
            estimated_total_time=int(total_time),
            stations_utilization=stations_utilization,
            generated_at=datetime.utcnow(),
            algorithm_version=ai_sequencer.algorithm_version
        )
        
        logger.info(f"Successfully sequenced {len(sequenced_orders)} orders")
        return response
        
    except Exception as e:
        logger.error(f"Failed to sequence orders: {e}")
        raise HTTPException(status_code=500, detail="Failed to sequence orders")


@router.get("/current", response_model=SequenceResponse)
async def get_current_sequence(
    station: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Get current order sequence.
    """
    try:
        orders = order_service.get_orders_for_sequencing(
            db, station_filter=station
        )
        
        if not orders:
            return SequenceResponse(
                sequenced_orders=[],
                total_orders=0,
                estimated_total_time=0,
                stations_utilization={},
                generated_at=datetime.utcnow()
            )
        
        # Convert existing orders to sequenced format
        sequenced_order_schemas = []
        current_time = datetime.utcnow()
        
        for i, order in enumerate(orders):
            # Use existing sequence position or assign new one
            sequence_position = order.sequence_position or (i + 1)
            
            # Estimate timing based on prep time
            total_prep_time = order.total_prep_time or 15  # default 15 minutes
            start_time = current_time + timedelta(minutes=i * 5)  # stagger starts
            completion_time = start_time + timedelta(minutes=total_prep_time)
            
            schema = SequencedOrderSchema(
                order=order,
                sequence_position=sequence_position,
                estimated_start_time=start_time,
                estimated_completion_time=completion_time,
                assigned_station=order.assigned_station or "GENERAL",
                priority_score=5.0,  # Default score
                reasoning="Current sequence"
            )
            sequenced_order_schemas.append(schema)
        
        stations_utilization = ai_sequencer.get_station_utilization()
        
        response = SequenceResponse(
            sequenced_orders=sequenced_order_schemas,
            total_orders=len(orders),
            estimated_total_time=sum(order.total_prep_time or 15 for order in orders),
            stations_utilization=stations_utilization,
            generated_at=datetime.utcnow()
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Failed to get current sequence: {e}")
        raise HTTPException(status_code=500, detail="Failed to get current sequence")


@router.post("/refresh")
async def refresh_sequence(
    background_tasks: BackgroundTasks,
    station: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Force refresh the order sequence.
    """
    try:
        # Reset station capacities
        ai_sequencer.reset_station_capacities()
        
        # Get orders and re-sequence
        orders = order_service.get_orders_for_sequencing(
            db, station_filter=station
        )
        
        if orders:
            sequenced_orders = ai_sequencer.sequence_orders(orders, db)
            
            # Update database
            background_tasks.add_task(
                order_service.update_order_sequences, db, sequenced_orders
            )
            
            # Notify backend
            background_tasks.add_task(
                order_service.notify_nodejs_backend,
                "sequence_refreshed",
                {"total_orders": len(sequenced_orders), "station": station}
            )
        
        return {
            "message": "Sequence refresh initiated",
            "orders_processed": len(orders) if orders else 0,
            "timestamp": datetime.utcnow()
        }
        
    except Exception as e:
        logger.error(f"Failed to refresh sequence: {e}")
        raise HTTPException(status_code=500, detail="Failed to refresh sequence")


@router.get("/metrics")
async def get_sequencing_metrics(db: Session = Depends(get_db)):
    """
    Get sequencing and order metrics.
    """
    try:
        # Get order metrics
        order_metrics = order_service.calculate_order_metrics(db)
        
        # Get station workload
        station_workload = order_service.get_station_workload(db)
        
        # Get delayed orders
        delayed_orders = order_service.get_delayed_orders(db)
        
        metrics = {
            "order_metrics": order_metrics,
            "station_workload": station_workload,
            "delayed_orders_count": len(delayed_orders),
            "delayed_orders": [
                {
                    "id": order.id,
                    "order_number": order.order_number,
                    "minutes_delayed": order.minutes_since_created,
                    "reason": order.delay_reason
                }
                for order in delayed_orders[:10]  # Limit to 10 most recent
            ],
            "stations_utilization": ai_sequencer.get_station_utilization(),
            "timestamp": datetime.utcnow()
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to get sequencing metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get metrics")
