"""
Tests for AI sequencer service.
"""
import pytest
from datetime import datetime

from app.services.ai_sequencer import AISequencer
from app.models.order import OrderPriority, OrderType


class TestAISequencer:
    """Test cases for AI sequencer."""
    
    def test_sequencer_initialization(self):
        """Test sequencer initializes correctly."""
        sequencer = AISequencer()
        assert sequencer.algorithm_version == "1.0"
        assert len(sequencer.station_capacities) > 0
        assert "GRILL" in sequencer.station_capacities
        assert "FRYER" in sequencer.station_capacities
    
    def test_priority_score_calculation(self, sample_orders):
        """Test priority score calculation."""
        sequencer = AISequencer()
        
        # Test VIP order
        vip_order = sample_orders[1]  # VIP order
        vip_score = sequencer._calculate_priority_score(vip_order)
        
        # Test normal order
        normal_order = sample_orders[0]  # Normal order
        normal_score = sequencer._calculate_priority_score(normal_order)
        
        # VIP should have higher score
        assert vip_score > normal_score
        assert vip_score >= 3.0  # VIP multiplier
    
    def test_time_score_calculation(self, sample_orders):
        """Test time-based score calculation."""
        sequencer = AISequencer()
        
        order = sample_orders[0]
        
        # Mock different waiting times
        order.created_at = datetime.utcnow()
        
        # Test recent order (should have lower time score)
        time_score = sequencer._calculate_time_score(order)
        assert 0 <= time_score <= 10
    
    def test_station_utilization(self):
        """Test station utilization tracking."""
        sequencer = AISequencer()
        
        # Initially all stations should be empty
        utilization = sequencer.get_station_utilization()
        assert all(load == 0 for load in utilization.values())
        
        # Update station capacity
        completion_time = datetime.utcnow()
        sequencer._update_station_capacity("GRILL", completion_time)
        
        # Check utilization updated
        updated_utilization = sequencer.get_station_utilization()
        assert updated_utilization["GRILL"] == 1
    
    def test_sequence_orders(self, db_session, sample_orders, sample_menu_items):
        """Test order sequencing functionality."""
        sequencer = AISequencer()
        
        # Sequence the orders
        sequenced_orders = sequencer.sequence_orders(sample_orders, db_session)
        
        # Check results
        assert len(sequenced_orders) == len(sample_orders)
        
        # Check that VIP order comes first (higher priority)
        vip_order_seq = next(
            (seq for seq in sequenced_orders if seq["order"].priority == OrderPriority.VIP),
            None
        )
        normal_order_seq = next(
            (seq for seq in sequenced_orders if seq["order"].priority == OrderPriority.NORMAL),
            None
        )
        
        assert vip_order_seq is not None
        assert normal_order_seq is not None
        assert vip_order_seq["sequence_position"] < normal_order_seq["sequence_position"]
    
    def test_station_assignment(self, db_session, sample_orders, sample_menu_items):
        """Test station assignment logic."""
        sequencer = AISequencer()
        
        order = sample_orders[0]  # Order with grill and fryer items
        current_time = datetime.utcnow()
        
        station, start_time, completion_time = sequencer._assign_station_and_timing(
            order, current_time, db_session
        )
        
        # Should assign to one of the required stations
        assert station in ["GRILL", "FRYER", "GENERAL"]
        assert start_time >= current_time
        assert completion_time > start_time
    
    def test_reasoning_generation(self, sample_orders):
        """Test reasoning text generation."""
        sequencer = AISequencer()
        
        order = sample_orders[1]  # VIP delayed order
        
        reasoning = sequencer._generate_reasoning(
            order, 
            priority_score=9.0,
            time_score=8.0,
            complexity_score=6.0,
            station_score=7.0
        )
        
        assert isinstance(reasoning, str)
        assert len(reasoning) > 0
        # Should mention VIP and delayed status
        assert "VIP" in reasoning or "delayed" in reasoning
    
    def test_reset_station_capacities(self):
        """Test station capacity reset functionality."""
        sequencer = AISequencer()
        
        # Add some capacity
        completion_time = datetime.utcnow()
        sequencer._update_station_capacity("GRILL", completion_time)
        
        # Verify capacity added
        assert sequencer.get_station_utilization()["GRILL"] == 1
        
        # Reset capacities
        sequencer.reset_station_capacities()
        
        # Should still have capacity (completion time is in future)
        assert sequencer.get_station_utilization()["GRILL"] >= 0
