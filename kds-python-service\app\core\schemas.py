"""
Pydantic schemas for API requests and responses.
"""
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class OrderStatusEnum(str, Enum):
    PENDING = "PENDING"
    CONFIRMED = "CONFIRMED"
    PREPARING = "PREPARING"
    READY = "READY"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"


class OrderTypeEnum(str, Enum):
    DINE_IN = "DINE_IN"
    TAKEAWAY = "TAKEAWAY"
    DELIVERY = "DELIVERY"


class OrderPriorityEnum(str, Enum):
    LOW = "LOW"
    NORMAL = "NORMAL"
    HIGH = "HIGH"
    VIP = "VIP"
    URGENT = "URGENT"


class MenuItemSchema(BaseModel):
    """Menu item schema."""
    id: int
    name: str
    category: str
    prep_time: int
    station_required: Optional[str] = None
    complexity_level: int = 1
    
    class Config:
        from_attributes = True


class OrderItemSchema(BaseModel):
    """Order item schema."""
    id: int
    menu_item_id: int
    quantity: int
    prep_time: Optional[int] = None
    station_required: Optional[str] = None
    modifications: Optional[str] = None
    menu_item: Optional[MenuItemSchema] = None
    
    class Config:
        from_attributes = True


class OrderSchema(BaseModel):
    """Order schema."""
    id: int
    order_number: str
    customer_name: Optional[str] = None
    table_number: Optional[int] = None
    order_type: OrderTypeEnum
    status: OrderStatusEnum
    priority: OrderPriorityEnum
    created_at: datetime
    estimated_prep_time: Optional[int] = None
    promised_time: Optional[datetime] = None
    total_amount: float
    special_instructions: Optional[str] = None
    sequence_position: Optional[int] = None
    assigned_station: Optional[str] = None
    is_delayed: bool = False
    order_items: List[OrderItemSchema] = []
    
    class Config:
        from_attributes = True


class SequenceRequest(BaseModel):
    """Request schema for order sequencing."""
    order_ids: Optional[List[int]] = None
    station_filter: Optional[str] = None
    max_orders: Optional[int] = 50
    force_refresh: bool = False


class SequencedOrderSchema(BaseModel):
    """Sequenced order schema with AI recommendations."""
    order: OrderSchema
    sequence_position: int
    estimated_start_time: datetime
    estimated_completion_time: datetime
    assigned_station: str
    priority_score: float
    reasoning: str


class SequenceResponse(BaseModel):
    """Response schema for order sequencing."""
    sequenced_orders: List[SequencedOrderSchema]
    total_orders: int
    estimated_total_time: int  # in minutes
    stations_utilization: Dict[str, int]
    generated_at: datetime
    algorithm_version: str = "1.0"


class HealthCheckResponse(BaseModel):
    """Health check response schema."""
    status: str
    timestamp: datetime
    version: str
    database_connected: bool
    ai_agent_status: str


class ErrorResponse(BaseModel):
    """Error response schema."""
    error: str
    message: str
    timestamp: datetime
    request_id: Optional[str] = None
