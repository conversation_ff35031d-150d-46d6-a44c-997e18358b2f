"""
Configuration settings for the KDS Python microservice.
"""
from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    app_name: str = "KDS AI Sequencing Service"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # Database
    database_url: str = "postgresql://user:password@localhost:5432/kds_db"
    database_pool_size: int = 10
    database_max_overflow: int = 20
    
    # Node.js Backend API
    nodejs_api_base_url: str = "http://localhost:3000/api"
    nodejs_api_timeout: int = 30
    
    # AI Agent Configuration
    ai_agent_enabled: bool = True
    max_orders_per_sequence: int = 50
    sequence_refresh_interval: int = 30  # seconds
    
    # Order Priority Weights
    priority_weight_vip: float = 3.0
    priority_weight_delayed: float = 2.5
    priority_weight_delivery: float = 1.5
    priority_weight_dine_in: float = 1.0
    priority_weight_takeaway: float = 1.2
    
    # Preparation Time Estimates (in minutes)
    prep_time_appetizer: int = 5
    prep_time_main_course: int = 15
    prep_time_dessert: int = 8
    prep_time_beverage: int = 2
    prep_time_default: int = 10
    
    # Kitchen Station Configuration
    stations_parallel: bool = True
    max_concurrent_orders_per_station: int = 5
    
    # Logging
    log_level: str = "INFO"
    log_format: str = "json"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings()
