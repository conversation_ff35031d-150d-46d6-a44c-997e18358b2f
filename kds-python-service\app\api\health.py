"""
Health check API endpoints.
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from datetime import datetime
import logging

from app.core.database import get_db, check_db_connection
from app.core.config import settings
from app.core.schemas import HealthCheckResponse

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=HealthCheckResponse)
async def health_check(db: Session = Depends(get_db)):
    """
    Health check endpoint.
    """
    try:
        # Check database connection
        db_connected = check_db_connection()
        
        # Check AI agent status
        ai_status = "enabled" if settings.ai_agent_enabled else "disabled"
        
        return HealthCheckResponse(
            status="healthy" if db_connected else "unhealthy",
            timestamp=datetime.utcnow(),
            version=settings.app_version,
            database_connected=db_connected,
            ai_agent_status=ai_status
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthCheckResponse(
            status="unhealthy",
            timestamp=datetime.utcnow(),
            version=settings.app_version,
            database_connected=False,
            ai_agent_status="error"
        )


@router.get("/ready")
async def readiness_check():
    """
    Readiness check for Kubernetes/container orchestration.
    """
    try:
        db_connected = check_db_connection()
        if not db_connected:
            return {"status": "not ready", "reason": "database not connected"}
        
        return {"status": "ready"}
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return {"status": "not ready", "reason": str(e)}


@router.get("/live")
async def liveness_check():
    """
    Liveness check for Kubernetes/container orchestration.
    """
    return {"status": "alive", "timestamp": datetime.utcnow()}
