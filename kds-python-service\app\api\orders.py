"""
Orders API endpoints with AI sequencing.
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional, Dict
import logging
from datetime import datetime

from app.core.database import get_db
from app.models.order import Order, OrderStatus, OrderType, OrderPriority
from app.models.order_item import OrderItem
from app.models.menu_item import MenuItem
from app.core.schemas import OrderSchema, OrderStatusEnum
from app.ai_agent import ai_agent

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/", response_model=List[OrderSchema])
async def get_orders(
    status: Optional[OrderStatusEnum] = None,
    order_type: Optional[str] = None,
    priority: Optional[str] = None,
    limit: int = Query(50, le=100),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db)
):
    """
    Get orders with optional filtering.
    """
    try:
        query = db.query(Order)
        
        # Apply filters
        if status:
            query = query.filter(Order.status == status.value)
        
        if order_type:
            query = query.filter(Order.order_type == order_type)
        
        if priority:
            query = query.filter(Order.priority == priority)
        
        # Get orders for KDS (exclude completed and cancelled)
        query = query.filter(
            Order.status.in_([
                OrderStatus.CONFIRMED,
                OrderStatus.PREPARING,
                OrderStatus.READY
            ])
        )
        
        # Order by priority and creation time
        query = query.order_by(
            Order.priority.desc(),
            Order.created_at.asc()
        )
        
        orders = query.offset(offset).limit(limit).all()
        
        return orders
    
    except Exception as e:
        logger.error(f"Failed to get orders: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve orders")


@router.get("/pending", response_model=List[OrderSchema])
async def get_pending_orders(
    station: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Get pending orders for KDS display.
    """
    try:
        query = db.query(Order).filter(
            Order.status.in_([
                OrderStatus.CONFIRMED,
                OrderStatus.PREPARING
            ])
        )
        
        if station:
            # Filter by station if specified
            query = query.join(OrderItem).join(MenuItem).filter(
                or_(
                    MenuItem.station_required == station,
                    MenuItem.secondary_stations.contains(station)
                )
            )
        
        orders = query.order_by(
            Order.sequence_position.asc().nullslast(),
            Order.priority.desc(),
            Order.created_at.asc()
        ).all()
        
        return orders
    
    except Exception as e:
        logger.error(f"Failed to get pending orders: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve pending orders")


@router.get("/{order_id}", response_model=OrderSchema)
async def get_order(order_id: int, db: Session = Depends(get_db)):
    """
    Get a specific order by ID.
    """
    try:
        order = db.query(Order).filter(Order.id == order_id).first()
        
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")
        
        return order
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get order {order_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve order")


@router.patch("/{order_id}/status")
async def update_order_status(
    order_id: int,
    status: OrderStatusEnum,
    db: Session = Depends(get_db)
):
    """
    Update order status.
    """
    try:
        order = db.query(Order).filter(Order.id == order_id).first()
        
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")
        
        order.status = status.value
        db.commit()
        db.refresh(order)
        
        logger.info(f"Updated order {order_id} status to {status.value}")
        
        return {"message": "Order status updated successfully", "order_id": order_id, "status": status.value}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update order {order_id} status: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update order status")


@router.get("/stats/summary")
async def get_order_stats(db: Session = Depends(get_db)):
    """
    Get order statistics for dashboard.
    """
    try:
        stats = {}
        
        # Count orders by status
        for status in OrderStatus:
            count = db.query(Order).filter(Order.status == status).count()
            stats[f"{status.value.lower()}_orders"] = count
        
        # Count orders by type
        for order_type in OrderType:
            count = db.query(Order).filter(Order.order_type == order_type).count()
            stats[f"{order_type.value.lower()}_orders"] = count
        
        # Count delayed orders
        delayed_count = db.query(Order).filter(Order.is_delayed == True).count()
        stats["delayed_orders"] = delayed_count
        
        # Count VIP orders
        vip_count = db.query(Order).filter(Order.priority == OrderPriority.VIP).count()
        stats["vip_orders"] = vip_count
        
        return stats

    except Exception as e:
        logger.error(f"Failed to get order stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve order statistics")


@router.post("/sequence")
async def sequence_orders_with_ai(
    station_capacity: Optional[Dict[str, int]] = None,
    prioritize_fifo: bool = True,
    db: Session = Depends(get_db)
):
    """
    Sequence orders using AI based on parameters.
    """
    try:
        # Get pending orders
        orders = db.query(Order).filter(
            Order.status.in_([OrderStatus.CONFIRMED, OrderStatus.PREPARING])
        ).order_by(Order.created_at.asc()).all()

        if not orders:
            return {"sequenced_orders": [], "message": "No orders to sequence"}

        # Prepare order data for AI
        order_data = []
        for order in orders:
            order_info = {
                "id": order.id,
                "order_number": order.order_number,
                "order_type": order.order_type.value,
                "priority": order.priority.value,
                "created_at": order.created_at.isoformat(),
                "estimated_prep_time": order.estimated_prep_time or 15,
                "table_number": order.table_number,
                "is_delayed": order.is_delayed,
                "items": []
            }

            # Add order items with station requirements
            for item in order.order_items:
                if item.menu_item:
                    order_info["items"].append({
                        "name": item.menu_item.name,
                        "quantity": item.quantity,
                        "prep_time": item.menu_item.prep_time,
                        "station_required": item.menu_item.station_required,
                        "complexity": item.menu_item.complexity_level
                    })

            order_data.append(order_info)

        # Default station capacity if not provided
        if not station_capacity:
            station_capacity = {
                "GRILL": 3,
                "FRYER": 2,
                "OVEN": 2,
                "STOVETOP": 2,
                "COLD_PREP": 4,
                "ASSEMBLY": 5
            }

        # Use AI agent for sequencing
        sequenced_orders = await ai_agent.sequence_orders(order_data, station_capacity, prioritize_fifo)

        return {
            "sequenced_orders": sequenced_orders,
            "total_orders": len(sequenced_orders),
            "parameters_used": {
                "station_capacity": station_capacity,
                "prioritize_fifo": prioritize_fifo
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to sequence orders with AI: {e}")
        raise HTTPException(status_code=500, detail="Failed to sequence orders")


@router.get("/ai-agent/info")
async def get_ai_agent_info():
    """
    Get AI agent configuration and status.
    """
    try:
        return ai_agent.get_model_info()
    except Exception as e:
        logger.error(f"Failed to get AI agent info: {e}")
        raise HTTPException(status_code=500, detail="Failed to get AI agent info")


@router.post("/ai-agent/configure")
async def configure_ai_agent(
    model: Optional[str] = None,
    temperature: Optional[float] = None,
    max_tokens: Optional[int] = None
):
    """
    Configure AI agent settings.
    """
    try:
        ai_agent.update_model_settings(model, temperature, max_tokens)
        return {
            "message": "AI agent configured successfully",
            "current_settings": ai_agent.get_model_info()
        }
    except Exception as e:
        logger.error(f"Failed to configure AI agent: {e}")
        raise HTTPException(status_code=500, detail="Failed to configure AI agent")



