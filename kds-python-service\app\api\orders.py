"""
Orders API endpoints with AI sequencing.
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional, Dict, Any
import logging
import json
from datetime import datetime
import openai
import os

from app.core.database import get_db
from app.models.order import Order, OrderStatus, OrderType, OrderPriority
from app.models.order_item import OrderItem
from app.models.menu_item import MenuItem
from app.core.schemas import OrderSchema, OrderStatusEnum

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize OpenAI
openai.api_key = os.getenv("OPENAI_API_KEY")

@router.get("/", response_model=List[OrderSchema])
async def get_orders(
    status: Optional[OrderStatusEnum] = None,
    order_type: Optional[str] = None,
    priority: Optional[str] = None,
    limit: int = Query(50, le=100),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db)
):
    """
    Get orders with optional filtering.
    """
    try:
        query = db.query(Order)
        
        # Apply filters
        if status:
            query = query.filter(Order.status == status.value)
        
        if order_type:
            query = query.filter(Order.order_type == order_type)
        
        if priority:
            query = query.filter(Order.priority == priority)
        
        # Get orders for KDS (exclude completed and cancelled)
        query = query.filter(
            Order.status.in_([
                OrderStatus.CONFIRMED,
                OrderStatus.PREPARING,
                OrderStatus.READY
            ])
        )
        
        # Order by priority and creation time
        query = query.order_by(
            Order.priority.desc(),
            Order.created_at.asc()
        )
        
        orders = query.offset(offset).limit(limit).all()
        
        return orders
    
    except Exception as e:
        logger.error(f"Failed to get orders: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve orders")


@router.get("/pending", response_model=List[OrderSchema])
async def get_pending_orders(
    station: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Get pending orders for KDS display.
    """
    try:
        query = db.query(Order).filter(
            Order.status.in_([
                OrderStatus.CONFIRMED,
                OrderStatus.PREPARING
            ])
        )
        
        if station:
            # Filter by station if specified
            query = query.join(OrderItem).join(MenuItem).filter(
                or_(
                    MenuItem.station_required == station,
                    MenuItem.secondary_stations.contains(station)
                )
            )
        
        orders = query.order_by(
            Order.sequence_position.asc().nullslast(),
            Order.priority.desc(),
            Order.created_at.asc()
        ).all()
        
        return orders
    
    except Exception as e:
        logger.error(f"Failed to get pending orders: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve pending orders")


@router.get("/{order_id}", response_model=OrderSchema)
async def get_order(order_id: int, db: Session = Depends(get_db)):
    """
    Get a specific order by ID.
    """
    try:
        order = db.query(Order).filter(Order.id == order_id).first()
        
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")
        
        return order
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get order {order_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve order")


@router.patch("/{order_id}/status")
async def update_order_status(
    order_id: int,
    status: OrderStatusEnum,
    db: Session = Depends(get_db)
):
    """
    Update order status.
    """
    try:
        order = db.query(Order).filter(Order.id == order_id).first()
        
        if not order:
            raise HTTPException(status_code=404, detail="Order not found")
        
        order.status = status.value
        db.commit()
        db.refresh(order)
        
        logger.info(f"Updated order {order_id} status to {status.value}")
        
        return {"message": "Order status updated successfully", "order_id": order_id, "status": status.value}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update order {order_id} status: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update order status")


@router.get("/stats/summary")
async def get_order_stats(db: Session = Depends(get_db)):
    """
    Get order statistics for dashboard.
    """
    try:
        stats = {}
        
        # Count orders by status
        for status in OrderStatus:
            count = db.query(Order).filter(Order.status == status).count()
            stats[f"{status.value.lower()}_orders"] = count
        
        # Count orders by type
        for order_type in OrderType:
            count = db.query(Order).filter(Order.order_type == order_type).count()
            stats[f"{order_type.value.lower()}_orders"] = count
        
        # Count delayed orders
        delayed_count = db.query(Order).filter(Order.is_delayed == True).count()
        stats["delayed_orders"] = delayed_count
        
        # Count VIP orders
        vip_count = db.query(Order).filter(Order.priority == OrderPriority.VIP).count()
        stats["vip_orders"] = vip_count
        
        return stats

    except Exception as e:
        logger.error(f"Failed to get order stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve order statistics")


@router.post("/sequence")
async def sequence_orders_with_ai(
    station_capacity: Optional[Dict[str, int]] = None,
    prioritize_fifo: bool = True,
    db: Session = Depends(get_db)
):
    """
    Sequence orders using AI based on parameters.
    """
    try:
        # Get pending orders
        orders = db.query(Order).filter(
            Order.status.in_([OrderStatus.CONFIRMED, OrderStatus.PREPARING])
        ).order_by(Order.created_at.asc()).all()

        if not orders:
            return {"sequenced_orders": [], "message": "No orders to sequence"}

        # Prepare order data for AI
        order_data = []
        for order in orders:
            order_info = {
                "id": order.id,
                "order_number": order.order_number,
                "order_type": order.order_type.value,
                "priority": order.priority.value,
                "created_at": order.created_at.isoformat(),
                "estimated_prep_time": order.estimated_prep_time or 15,
                "table_number": order.table_number,
                "is_delayed": order.is_delayed,
                "items": []
            }

            # Add order items with station requirements
            for item in order.order_items:
                if item.menu_item:
                    order_info["items"].append({
                        "name": item.menu_item.name,
                        "quantity": item.quantity,
                        "prep_time": item.menu_item.prep_time,
                        "station_required": item.menu_item.station_required,
                        "complexity": item.menu_item.complexity_level
                    })

            order_data.append(order_info)

        # Default station capacity if not provided
        if not station_capacity:
            station_capacity = {
                "GRILL": 3,
                "FRYER": 2,
                "OVEN": 2,
                "STOVETOP": 2,
                "COLD_PREP": 4,
                "ASSEMBLY": 5
            }

        # Create AI prompt for sequencing
        sequenced_orders = await get_ai_sequence(order_data, station_capacity, prioritize_fifo)

        return {
            "sequenced_orders": sequenced_orders,
            "total_orders": len(sequenced_orders),
            "parameters_used": {
                "station_capacity": station_capacity,
                "prioritize_fifo": prioritize_fifo
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to sequence orders with AI: {e}")
        raise HTTPException(status_code=500, detail="Failed to sequence orders")


async def get_ai_sequence(orders: List[Dict], station_capacity: Dict[str, int], prioritize_fifo: bool) -> List[Dict]:
    """
    Use OpenAI to sequence orders based on parameters.
    """
    try:
        # Create the AI prompt
        prompt = f"""
You are a Kitchen Display System AI that sequences orders for optimal kitchen efficiency.

PARAMETERS:
- Station Capacity: {json.dumps(station_capacity)}
- Prioritize FIFO (First In, First Out): {prioritize_fifo}

ORDERS TO SEQUENCE:
{json.dumps(orders, indent=2)}

SEQUENCING RULES:
1. VIP and URGENT orders get highest priority
2. Consider station capacity - don't overload stations
3. Balance preparation times across stations
4. If FIFO is enabled, older orders generally come first (unless VIP/URGENT)
5. Group orders that use same stations when possible
6. Consider order complexity and prep time

RESPONSE FORMAT (JSON only):
{{
  "sequenced_orders": [
    {{
      "sequence_position": 1,
      "order_id": 123,
      "order_number": "ORD-001",
      "assigned_station": "GRILL",
      "estimated_start_time": "2024-01-01T10:00:00",
      "estimated_completion_time": "2024-01-01T10:15:00",
      "reasoning": "VIP order with simple prep time"
    }}
  ]
}}

Return ONLY the JSON response, no other text.
"""

        # Call OpenAI API
        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a kitchen sequencing AI. Return only JSON responses."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=2000
        )

        # Parse AI response
        ai_response = response.choices[0].message.content.strip()
        result = json.loads(ai_response)

        return result.get("sequenced_orders", [])

    except Exception as e:
        logger.error(f"AI sequencing failed: {e}")
        # Fallback to simple FIFO if AI fails
        return create_fallback_sequence(orders)


def create_fallback_sequence(orders: List[Dict]) -> List[Dict]:
    """
    Create a simple fallback sequence if AI fails.
    """
    sequenced = []
    current_time = datetime.now()

    # Sort by priority and creation time
    sorted_orders = sorted(orders, key=lambda x: (
        0 if x["priority"] == "VIP" else 1 if x["priority"] == "URGENT" else 2,
        x["created_at"]
    ))

    for i, order in enumerate(sorted_orders):
        # Simple station assignment based on first item
        station = "GENERAL"
        if order["items"]:
            station = order["items"][0].get("station_required", "GENERAL")

        # Estimate timing
        prep_time = order["estimated_prep_time"]
        start_time = current_time.replace(microsecond=0)
        completion_time = start_time.replace(second=start_time.second + prep_time * 60)

        sequenced.append({
            "sequence_position": i + 1,
            "order_id": order["id"],
            "order_number": order["order_number"],
            "assigned_station": station,
            "estimated_start_time": start_time.isoformat(),
            "estimated_completion_time": completion_time.isoformat(),
            "reasoning": f"Fallback sequence - {order['priority']} priority"
        })

    return sequenced
