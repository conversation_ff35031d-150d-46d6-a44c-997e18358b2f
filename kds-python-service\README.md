# KDS AI Sequencing Service

A simple Python microservice that uses **OpenAI GPT** to intelligently sequence kitchen orders based on real-time parameters like station capacity, preparation time, FIFO priority, and order types.

## 🤖 AI-Powered Sequencing

This service gets orders from your database and uses OpenAI's GPT model to determine the optimal sequence based on:

- **Station Capacity**: Current kitchen station loads (grill, fryer, oven, etc.)
- **Preparation Time**: Estimated cooking times for each order
- **FIFO (First In, First Out)**: Time-based ordering preference
- **Order Priority**: VIP, urgent, and delayed order handling
- **Order Type**: Delivery, takeaway, dine-in considerations

## 🚀 Quick Start

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Node.js API   │    │  Python KDS     │    │   PostgreSQL    │
│   (Main Backend)│◄──►│  AI Service     │◄──►│   Database      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  AI Sequencer   │
                       │  Engine         │
                       └─────────────────┘
```

## Quick Start

### Prerequisites

- Python 3.11+
- PostgreSQL 12+
- Docker & Docker Compose (optional)

### Installation

1. **<PERSON>lone and setup the project:**

```bash
cd kds-python-service
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Configure environment:**

```bash
cp .env.example .env
# Edit .env with your database and API configurations
```

3. **Setup database:**

```bash
# Initialize Alembic
alembic init alembic
alembic revision --autogenerate -m "Initial migration"
alembic upgrade head
```

4. **Run the service:**

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f kds-python-service
```

## API Endpoints

### Health Check

- `GET /health/` - Service health status
- `GET /health/ready` - Readiness check
- `GET /health/live` - Liveness check

### Orders

- `GET /api/orders/` - Get orders with filtering
- `GET /api/orders/pending` - Get pending orders for KDS
- `GET /api/orders/{order_id}` - Get specific order
- `PATCH /api/orders/{order_id}/status` - Update order status
- `GET /api/orders/stats/summary` - Get order statistics

### Sequencing

- `POST /api/sequence/orders` - Sequence orders using AI
- `GET /api/sequence/current` - Get current sequence
- `POST /api/sequence/refresh` - Force refresh sequence
- `GET /api/sequence/metrics` - Get sequencing metrics

## AI Sequencing Algorithm

The AI sequencer considers multiple factors:

### Scoring Factors

1. **Priority Score (40% weight)**

   - VIP customers: 3.0x multiplier
   - Urgent orders: 2.8x multiplier
   - Delayed orders: 2.5x multiplier
   - Order type (delivery, takeaway, dine-in)

2. **Time Score (30% weight)**

   - Waiting time since order creation
   - Promised delivery times
   - SLA compliance

3. **Complexity Score (20% weight)**

   - Menu item complexity levels
   - Preparation time estimates
   - Special requirements

4. **Station Score (10% weight)**
   - Kitchen station availability
   - Current workload distribution
   - Equipment utilization

### Station Management

The system tracks capacity for different kitchen stations:

- **GRILL**: Grilled items
- **FRYER**: Fried items
- **OVEN**: Baked/roasted items
- **STOVETOP**: Pan-cooked items
- **COLD_PREP**: Salads, cold appetizers
- **ASSEMBLY**: Final assembly, plating

## Configuration

Key configuration options in `.env`:

```env
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/kds_db

# AI Settings
AI_AGENT_ENABLED=true
MAX_ORDERS_PER_SEQUENCE=50
SEQUENCE_REFRESH_INTERVAL=30

# Priority Weights
PRIORITY_WEIGHT_VIP=3.0
PRIORITY_WEIGHT_DELAYED=2.5

# Station Configuration
MAX_CONCURRENT_ORDERS_PER_STATION=5
```

## Integration with Node.js Backend

The service integrates with your existing Node.js backend:

1. **Database Sharing**: Connects to the same PostgreSQL database managed by Prisma
2. **API Communication**: Provides REST endpoints for order sequencing
3. **Event Notifications**: Sends updates back to Node.js via HTTP callbacks

### Example Integration

```javascript
// Node.js - Request order sequencing
const response = await fetch("http://localhost:8000/api/sequence/orders", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    station_filter: "GRILL",
    max_orders: 20,
    force_refresh: true,
  }),
});

const sequencedOrders = await response.json();
```

## Testing

```bash
# Run tests
pytest

# Run with coverage
pytest --cov=app tests/

# Run specific test file
pytest tests/test_ai_sequencer.py -v
```

## Monitoring

The service provides comprehensive monitoring:

- **Health Checks**: Kubernetes-ready health endpoints
- **Metrics**: Order processing metrics and station utilization
- **Logging**: Structured JSON logging with request tracing
- **Performance**: Request timing and processing metrics

## Development

### Project Structure

```
kds-python-service/
├── app/
│   ├── api/           # FastAPI routers
│   ├── core/          # Configuration, database, schemas
│   ├── models/        # SQLAlchemy models
│   ├── services/      # Business logic
│   └── main.py        # FastAPI application
├── tests/             # Test files
├── config/            # Configuration files
├── requirements.txt   # Python dependencies
├── Dockerfile         # Container configuration
└── docker-compose.yml # Multi-service setup
```

### Adding New Features

1. **New AI Algorithm**: Extend `AISequencer` class
2. **New Endpoints**: Add routers in `app/api/`
3. **Database Changes**: Create Alembic migrations
4. **New Models**: Add SQLAlchemy models in `app/models/`

## Deployment

### Production Considerations

1. **Database**: Use connection pooling and read replicas
2. **Caching**: Implement Redis for sequence caching
3. **Monitoring**: Add Prometheus metrics and Grafana dashboards
4. **Security**: Implement API authentication and rate limiting
5. **Scaling**: Use multiple service instances behind a load balancer

### Environment Variables

Ensure these are set in production:

- `DATABASE_URL`: Production database connection
- `NODEJS_API_BASE_URL`: Your Node.js backend URL
- `LOG_LEVEL`: Set to "INFO" or "WARNING"
- `DEBUG`: Set to "false"

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:

1. Check the logs: `docker-compose logs kds-python-service`
2. Verify database connectivity: `GET /health/`
3. Check API documentation: `http://localhost:8000/docs`
