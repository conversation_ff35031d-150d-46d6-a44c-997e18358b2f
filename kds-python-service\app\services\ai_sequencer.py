"""
AI-powered order sequencing service for KDS.
"""
from typing import List, Dict, Tu<PERSON>, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging
import numpy as np
from sqlalchemy.orm import Session

from app.models.order import Order, OrderPriority, OrderType, OrderStatus
from app.models.order_item import OrderItem
from app.models.menu_item import MenuItem
from app.core.config import settings

logger = logging.getLogger(__name__)


@dataclass
class OrderScore:
    """Order scoring data structure."""
    order_id: int
    priority_score: float
    time_score: float
    complexity_score: float
    station_score: float
    total_score: float
    reasoning: str


@dataclass
class StationCapacity:
    """Station capacity tracking."""
    station_name: str
    current_load: int
    max_capacity: int
    estimated_completion_times: List[datetime]


class AISequencer:
    """AI-powered order sequencing engine."""
    
    def __init__(self):
        self.algorithm_version = "1.0"
        self.station_capacities = self._initialize_station_capacities()
    
    def _initialize_station_capacities(self) -> Dict[str, StationCapacity]:
        """Initialize station capacity tracking."""
        stations = {
            "GRILL": StationCapacity("GRILL", 0, settings.max_concurrent_orders_per_station, []),
            "FRYER": StationCapacity("FRYER", 0, settings.max_concurrent_orders_per_station, []),
            "OVEN": StationCapacity("OVEN", 0, settings.max_concurrent_orders_per_station, []),
            "STOVETOP": StationCapacity("STOVETOP", 0, settings.max_concurrent_orders_per_station, []),
            "COLD_PREP": StationCapacity("COLD_PREP", 0, settings.max_concurrent_orders_per_station, []),
            "ASSEMBLY": StationCapacity("ASSEMBLY", 0, settings.max_concurrent_orders_per_station, []),
            "GENERAL": StationCapacity("GENERAL", 0, settings.max_concurrent_orders_per_station, [])
        }
        return stations
    
    def sequence_orders(self, orders: List[Order], db: Session) -> List[Dict]:
        """
        Main method to sequence orders using AI algorithm.
        """
        try:
            logger.info(f"Sequencing {len(orders)} orders")
            
            # Calculate scores for each order
            order_scores = []
            for order in orders:
                score = self._calculate_order_score(order, db)
                order_scores.append(score)
            
            # Sort orders by total score (descending)
            order_scores.sort(key=lambda x: x.total_score, reverse=True)
            
            # Assign sequence positions and stations
            sequenced_orders = []
            current_time = datetime.utcnow()
            
            for i, score in enumerate(order_scores):
                order = next(o for o in orders if o.id == score.order_id)
                
                # Determine best station and timing
                station, start_time, completion_time = self._assign_station_and_timing(
                    order, current_time, db
                )
                
                sequenced_order = {
                    "order": order,
                    "sequence_position": i + 1,
                    "estimated_start_time": start_time,
                    "estimated_completion_time": completion_time,
                    "assigned_station": station,
                    "priority_score": score.total_score,
                    "reasoning": score.reasoning
                }
                
                sequenced_orders.append(sequenced_order)
                
                # Update station capacity
                self._update_station_capacity(station, completion_time)
            
            logger.info(f"Successfully sequenced {len(sequenced_orders)} orders")
            return sequenced_orders
            
        except Exception as e:
            logger.error(f"Failed to sequence orders: {e}")
            raise
    
    def _calculate_order_score(self, order: Order, db: Session) -> OrderScore:
        """Calculate comprehensive score for an order."""
        
        # Priority score (0-10)
        priority_score = self._calculate_priority_score(order)
        
        # Time-based score (0-10)
        time_score = self._calculate_time_score(order)
        
        # Complexity score (0-10)
        complexity_score = self._calculate_complexity_score(order, db)
        
        # Station availability score (0-10)
        station_score = self._calculate_station_score(order, db)
        
        # Weighted total score
        weights = {
            "priority": 0.4,
            "time": 0.3,
            "complexity": 0.2,
            "station": 0.1
        }
        
        total_score = (
            priority_score * weights["priority"] +
            time_score * weights["time"] +
            complexity_score * weights["complexity"] +
            station_score * weights["station"]
        )
        
        # Generate reasoning
        reasoning = self._generate_reasoning(
            order, priority_score, time_score, complexity_score, station_score
        )
        
        return OrderScore(
            order_id=order.id,
            priority_score=priority_score,
            time_score=time_score,
            complexity_score=complexity_score,
            station_score=station_score,
            total_score=total_score,
            reasoning=reasoning
        )
    
    def _calculate_priority_score(self, order: Order) -> float:
        """Calculate priority-based score."""
        priority_weights = {
            OrderPriority.VIP: settings.priority_weight_vip,
            OrderPriority.URGENT: 2.8,
            OrderPriority.HIGH: 2.0,
            OrderPriority.NORMAL: 1.0,
            OrderPriority.LOW: 0.5
        }
        
        base_score = priority_weights.get(order.priority, 1.0)
        
        # Boost for delayed orders
        if order.is_delayed:
            base_score *= settings.priority_weight_delayed
        
        # Order type multiplier
        type_weights = {
            OrderType.DELIVERY: settings.priority_weight_delivery,
            OrderType.TAKEAWAY: settings.priority_weight_takeaway,
            OrderType.DINE_IN: settings.priority_weight_dine_in
        }
        
        type_multiplier = type_weights.get(order.order_type, 1.0)
        
        return min(base_score * type_multiplier, 10.0)
    
    def _calculate_time_score(self, order: Order) -> float:
        """Calculate time-based urgency score."""
        minutes_waiting = order.minutes_since_created
        
        # Score increases with waiting time
        if minutes_waiting <= 5:
            return 2.0
        elif minutes_waiting <= 15:
            return 5.0
        elif minutes_waiting <= 30:
            return 7.0
        elif minutes_waiting <= 45:
            return 8.5
        else:
            return 10.0  # Very urgent
    
    def _calculate_complexity_score(self, order: Order, db: Session) -> float:
        """Calculate complexity-based score (lower complexity = higher score)."""
        if not order.order_items:
            return 5.0
        
        total_complexity = 0
        total_items = 0
        
        for item in order.order_items:
            if item.menu_item:
                complexity = item.menu_item.complexity_level
                total_complexity += complexity * item.quantity
                total_items += item.quantity
        
        if total_items == 0:
            return 5.0
        
        avg_complexity = total_complexity / total_items
        
        # Invert score (simpler orders get higher priority)
        return max(10.0 - (avg_complexity * 2), 1.0)
    
    def _calculate_station_score(self, order: Order, db: Session) -> float:
        """Calculate station availability score."""
        if not order.order_items:
            return 5.0
        
        required_stations = set()
        for item in order.order_items:
            if item.menu_item and item.menu_item.station_required:
                required_stations.add(item.menu_item.station_required)
        
        if not required_stations:
            required_stations.add("GENERAL")
        
        # Calculate average availability across required stations
        availability_scores = []
        for station in required_stations:
            if station in self.station_capacities:
                capacity = self.station_capacities[station]
                utilization = capacity.current_load / capacity.max_capacity
                availability_score = (1.0 - utilization) * 10.0
                availability_scores.append(availability_score)
            else:
                availability_scores.append(5.0)  # Default score
        
        return np.mean(availability_scores) if availability_scores else 5.0

    def _assign_station_and_timing(self, order: Order, current_time: datetime, db: Session) -> Tuple[str, datetime, datetime]:
        """Assign optimal station and calculate timing for an order."""

        # Determine required stations
        required_stations = set()
        total_prep_time = 0

        for item in order.order_items:
            if item.menu_item:
                station = item.menu_item.station_required or "GENERAL"
                required_stations.add(station)
                total_prep_time += item.total_prep_time_with_quantity

        if not required_stations:
            required_stations.add("GENERAL")

        # Find the best available station
        best_station = self._find_best_station(required_stations)

        # Calculate start time based on station availability
        station_capacity = self.station_capacities.get(best_station)
        if station_capacity and station_capacity.estimated_completion_times:
            # Start after the last order in this station completes
            start_time = max(station_capacity.estimated_completion_times[-1], current_time)
        else:
            start_time = current_time

        # Calculate completion time
        completion_time = start_time + timedelta(minutes=total_prep_time)

        return best_station, start_time, completion_time

    def _find_best_station(self, required_stations: set) -> str:
        """Find the best available station from required stations."""
        best_station = "GENERAL"
        lowest_load = float('inf')

        for station in required_stations:
            if station in self.station_capacities:
                capacity = self.station_capacities[station]
                if capacity.current_load < lowest_load:
                    lowest_load = capacity.current_load
                    best_station = station

        return best_station

    def _update_station_capacity(self, station: str, completion_time: datetime):
        """Update station capacity after assigning an order."""
        if station in self.station_capacities:
            capacity = self.station_capacities[station]
            capacity.current_load += 1
            capacity.estimated_completion_times.append(completion_time)

            # Sort completion times
            capacity.estimated_completion_times.sort()

    def _generate_reasoning(self, order: Order, priority_score: float, time_score: float,
                          complexity_score: float, station_score: float) -> str:
        """Generate human-readable reasoning for the sequencing decision."""
        reasons = []

        # Priority reasoning
        if priority_score >= 8.0:
            if order.priority == OrderPriority.VIP:
                reasons.append("VIP customer priority")
            elif order.is_delayed:
                reasons.append("delayed order requiring immediate attention")
            else:
                reasons.append("high priority order")

        # Time reasoning
        if time_score >= 8.0:
            reasons.append(f"waiting {order.minutes_since_created} minutes")
        elif time_score >= 6.0:
            reasons.append("moderate wait time")

        # Complexity reasoning
        if complexity_score >= 8.0:
            reasons.append("simple preparation")
        elif complexity_score <= 4.0:
            reasons.append("complex preparation")

        # Station reasoning
        if station_score >= 8.0:
            reasons.append("station readily available")
        elif station_score <= 4.0:
            reasons.append("station capacity limited")

        # Order type reasoning
        if order.order_type == OrderType.DELIVERY:
            reasons.append("delivery order")
        elif order.order_type == OrderType.TAKEAWAY:
            reasons.append("takeaway order")

        if not reasons:
            reasons.append("standard processing order")

        return "; ".join(reasons)

    def get_station_utilization(self) -> Dict[str, int]:
        """Get current station utilization."""
        utilization = {}
        for station, capacity in self.station_capacities.items():
            utilization[station] = capacity.current_load
        return utilization

    def reset_station_capacities(self):
        """Reset station capacities (call this periodically)."""
        current_time = datetime.utcnow()

        for station, capacity in self.station_capacities.items():
            # Remove completed orders
            capacity.estimated_completion_times = [
                t for t in capacity.estimated_completion_times if t > current_time
            ]
            capacity.current_load = len(capacity.estimated_completion_times)
