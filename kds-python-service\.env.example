# Application Configuration
APP_NAME="KDS AI Sequencing Service"
APP_VERSION="1.0.0"
DEBUG=false

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/kds_database"
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Node.js Backend API Configuration
NODEJS_API_BASE_URL="http://localhost:3000/api"
NODEJS_API_TIMEOUT=30

# AI Configuration
OPENAI_API_KEY="your-openai-api-key-here"
AI_AGENT_ENABLED=true
MAX_ORDERS_PER_SEQUENCE=50
SEQUENCE_REFRESH_INTERVAL=30

# Order Priority Weights
PRIORITY_WEIGHT_VIP=3.0
PRIORITY_WEIGHT_DELAYED=2.5
PRIORITY_WEIGHT_DELIVERY=1.5
PRIORITY_WEIGHT_DINE_IN=1.0
PRIORITY_WEIGHT_TAKEAWAY=1.2

# Preparation Time Estimates (in minutes)
PREP_TIME_APPETIZER=5
PREP_TIME_MAIN_COURSE=15
PREP_TIME_DESSERT=8
PREP_TIME_BEVERAGE=2
PREP_TIME_DEFAULT=10

# Kitchen Station Configuration
STATIONS_PARALLEL=true
MAX_CONCURRENT_ORDERS_PER_STATION=5

# Logging Configuration
LOG_LEVEL="INFO"
LOG_FORMAT="json"
