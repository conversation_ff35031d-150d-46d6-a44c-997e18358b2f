"""
Order item model for KDS system.
"""
from sqlalchemy import Column, Integer, String, Float, Boolean, Text, ForeignKey
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from app.core.database import Base


class ItemStatus(PyEnum):
    """Order item status enumeration."""
    PENDING = "PENDING"
    PREPARING = "PREPARING"
    READY = "READY"
    SERVED = "SERVED"


class OrderItem(Base):
    """Order item model."""
    
    __tablename__ = "order_items"
    
    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False)
    menu_item_id = Column(Integer, ForeignKey("menu_items.id"), nullable=False)
    
    # Item details
    quantity = Column(Integer, nullable=False, default=1)
    unit_price = Column(Float, nullable=False)
    total_price = Column(Float, nullable=False)
    
    # Preparation details
    status = Column(String(20), nullable=False, default=ItemStatus.PENDING.value)
    prep_time = Column(Integer, nullable=True)  # in minutes
    station_required = Column(String(50), nullable=True)  # grill, fryer, oven, etc.
    
    # Customizations
    modifications = Column(Text, nullable=True)
    special_requests = Column(Text, nullable=True)
    
    # Flags
    is_priority = Column(Boolean, default=False)
    requires_special_handling = Column(Boolean, default=False)
    
    # Relationships
    order = relationship("Order", back_populates="order_items")
    menu_item = relationship("MenuItem", back_populates="order_items")
    
    def __repr__(self):
        return f"<OrderItem(id={self.id}, order_id={self.order_id}, quantity={self.quantity})>"
    
    @property
    def estimated_prep_time(self) -> int:
        """Get estimated preparation time."""
        if self.prep_time:
            return self.prep_time
        if self.menu_item and self.menu_item.prep_time:
            return self.menu_item.prep_time
        return 10  # default prep time
    
    @property
    def total_prep_time_with_quantity(self) -> int:
        """Get total prep time considering quantity."""
        base_time = self.estimated_prep_time
        # Add extra time for multiple quantities (not linear scaling)
        if self.quantity > 1:
            extra_time = (self.quantity - 1) * (base_time * 0.3)  # 30% extra per additional item
            return int(base_time + extra_time)
        return base_time
