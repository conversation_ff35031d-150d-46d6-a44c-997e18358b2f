"""
Order service for managing order operations.
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from datetime import datetime, timedelta
import logging
import httpx

from app.models.order import Order, OrderStatus, OrderType, OrderPriority
from app.models.order_item import OrderItem
from app.models.menu_item import MenuItem
from app.core.config import settings

logger = logging.getLogger(__name__)


class OrderService:
    """Service for order management operations."""
    
    def __init__(self):
        self.nodejs_api_base_url = settings.nodejs_api_base_url
        self.api_timeout = settings.nodejs_api_timeout
    
    def get_orders_for_sequencing(self, db: Session, station_filter: Optional[str] = None, 
                                 max_orders: int = 50) -> List[Order]:
        """
        Get orders that need to be sequenced for KDS.
        """
        try:
            query = db.query(Order).filter(
                Order.status.in_([
                    OrderStatus.CONFIRMED,
                    OrderStatus.PREPARING
                ])
            )
            
            # Join with order items and menu items for station filtering
            if station_filter:
                query = query.join(OrderItem).join(MenuItem).filter(
                    or_(
                        MenuItem.station_required == station_filter,
                        MenuItem.secondary_stations.contains(station_filter)
                    )
                )
            
            # Order by current sequence position, then priority, then creation time
            orders = query.order_by(
                Order.sequence_position.asc().nullslast(),
                Order.priority.desc(),
                Order.created_at.asc()
            ).limit(max_orders).all()
            
            logger.info(f"Retrieved {len(orders)} orders for sequencing")
            return orders
            
        except Exception as e:
            logger.error(f"Failed to get orders for sequencing: {e}")
            raise
    
    def update_order_sequences(self, db: Session, sequenced_orders: List[Dict]) -> bool:
        """
        Update order sequence positions in the database.
        """
        try:
            for seq_order in sequenced_orders:
                order = seq_order["order"]
                sequence_position = seq_order["sequence_position"]
                assigned_station = seq_order["assigned_station"]
                
                # Update the order in database
                db_order = db.query(Order).filter(Order.id == order.id).first()
                if db_order:
                    db_order.sequence_position = sequence_position
                    db_order.assigned_station = assigned_station
                    db_order.updated_at = datetime.utcnow()
            
            db.commit()
            logger.info(f"Updated sequence positions for {len(sequenced_orders)} orders")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update order sequences: {e}")
            db.rollback()
            return False
    
    def mark_order_delayed(self, db: Session, order_id: int, reason: str = None) -> bool:
        """
        Mark an order as delayed.
        """
        try:
            order = db.query(Order).filter(Order.id == order_id).first()
            if not order:
                logger.warning(f"Order {order_id} not found for delay marking")
                return False
            
            order.is_delayed = True
            order.delay_reason = reason
            order.updated_at = datetime.utcnow()
            
            db.commit()
            logger.info(f"Marked order {order_id} as delayed: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to mark order {order_id} as delayed: {e}")
            db.rollback()
            return False
    
    def get_delayed_orders(self, db: Session) -> List[Order]:
        """
        Get all delayed orders.
        """
        try:
            orders = db.query(Order).filter(
                and_(
                    Order.is_delayed == True,
                    Order.status.in_([
                        OrderStatus.CONFIRMED,
                        OrderStatus.PREPARING
                    ])
                )
            ).order_by(Order.created_at.asc()).all()
            
            return orders
            
        except Exception as e:
            logger.error(f"Failed to get delayed orders: {e}")
            return []
    
    def calculate_order_metrics(self, db: Session) -> Dict[str, Any]:
        """
        Calculate various order metrics for monitoring.
        """
        try:
            metrics = {}
            
            # Average preparation time by order type
            for order_type in OrderType:
                avg_prep = db.query(Order).filter(
                    and_(
                        Order.order_type == order_type,
                        Order.estimated_prep_time.isnot(None)
                    )
                ).with_entities(Order.estimated_prep_time).all()
                
                if avg_prep:
                    avg_time = sum(p[0] for p in avg_prep) / len(avg_prep)
                    metrics[f"avg_prep_time_{order_type.value.lower()}"] = round(avg_time, 2)
            
            # Orders by priority distribution
            for priority in OrderPriority:
                count = db.query(Order).filter(Order.priority == priority).count()
                metrics[f"orders_{priority.value.lower()}"] = count
            
            # Current queue length
            queue_length = db.query(Order).filter(
                Order.status.in_([OrderStatus.CONFIRMED, OrderStatus.PREPARING])
            ).count()
            metrics["current_queue_length"] = queue_length
            
            # Delayed orders count
            delayed_count = db.query(Order).filter(Order.is_delayed == True).count()
            metrics["delayed_orders_count"] = delayed_count
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to calculate order metrics: {e}")
            return {}
    
    async def notify_nodejs_backend(self, event_type: str, data: Dict[str, Any]) -> bool:
        """
        Send notification to Node.js backend about sequencing updates.
        """
        try:
            async with httpx.AsyncClient(timeout=self.api_timeout) as client:
                response = await client.post(
                    f"{self.nodejs_api_base_url}/kds/notifications",
                    json={
                        "event_type": event_type,
                        "timestamp": datetime.utcnow().isoformat(),
                        "data": data
                    }
                )
                
                if response.status_code == 200:
                    logger.info(f"Successfully notified Node.js backend: {event_type}")
                    return True
                else:
                    logger.warning(f"Node.js backend notification failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to notify Node.js backend: {e}")
            return False
    
    def get_station_workload(self, db: Session) -> Dict[str, Dict[str, Any]]:
        """
        Get current workload for each station.
        """
        try:
            workload = {}
            
            # Get all active orders with their items
            active_orders = db.query(Order).filter(
                Order.status.in_([OrderStatus.CONFIRMED, OrderStatus.PREPARING])
            ).all()
            
            station_orders = {}
            station_prep_times = {}
            
            for order in active_orders:
                for item in order.order_items:
                    if item.menu_item and item.menu_item.station_required:
                        station = item.menu_item.station_required
                        
                        if station not in station_orders:
                            station_orders[station] = 0
                            station_prep_times[station] = 0
                        
                        station_orders[station] += item.quantity
                        station_prep_times[station] += item.total_prep_time_with_quantity
            
            # Format workload data
            for station, order_count in station_orders.items():
                workload[station] = {
                    "active_orders": order_count,
                    "total_prep_time": station_prep_times[station],
                    "avg_prep_time": round(station_prep_times[station] / order_count, 2) if order_count > 0 else 0,
                    "utilization_level": min(order_count / settings.max_concurrent_orders_per_station, 1.0)
                }
            
            return workload
            
        except Exception as e:
            logger.error(f"Failed to get station workload: {e}")
            return {}
