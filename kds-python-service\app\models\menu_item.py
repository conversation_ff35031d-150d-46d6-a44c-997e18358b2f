"""
Menu item model for KDS system.
"""
from sqlalchemy import Column, Integer, String, Float, Boolean, Text
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from app.core.database import Base


class MenuCategory(PyEnum):
    """Menu category enumeration."""
    APPETIZER = "APPETIZER"
    MAIN_COURSE = "MAIN_COURSE"
    DESSERT = "DESSERT"
    BEVERAGE = "BEVERAGE"
    SIDE = "SIDE"
    SPECIAL = "SPECIAL"


class CookingMethod(PyEnum):
    """Cooking method enumeration."""
    GRILL = "GRILL"
    FRYER = "FRYER"
    OVEN = "OVEN"
    STOVETOP = "STOVETOP"
    COLD_PREP = "COLD_PREP"
    ASSEMBLY = "ASSEMBLY"


class MenuItem(Base):
    """Menu item model."""
    
    __tablename__ = "menu_items"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # Categorization
    category = Column(String(50), nullable=False)
    subcategory = Column(String(50), nullable=True)
    
    # Pricing
    price = Column(Float, nullable=False)
    cost = Column(Float, nullable=True)
    
    # Kitchen information
    prep_time = Column(Integer, nullable=False, default=10)  # in minutes
    cooking_method = Column(String(50), nullable=True)
    station_required = Column(String(50), nullable=True)  # primary station
    secondary_stations = Column(String(200), nullable=True)  # comma-separated
    
    # Complexity and difficulty
    complexity_level = Column(Integer, default=1)  # 1-5 scale
    skill_level_required = Column(String(20), default="BASIC")  # BASIC, INTERMEDIATE, ADVANCED
    
    # Availability
    is_available = Column(Boolean, default=True)
    is_seasonal = Column(Boolean, default=False)
    
    # Special handling
    requires_special_equipment = Column(Boolean, default=False)
    allergen_info = Column(String(200), nullable=True)
    dietary_restrictions = Column(String(200), nullable=True)  # vegan, gluten-free, etc.
    
    # Relationships
    order_items = relationship("OrderItem", back_populates="menu_item")
    
    def __repr__(self):
        return f"<MenuItem(id={self.id}, name='{self.name}', category='{self.category}')>"
    
    @property
    def estimated_prep_time_with_complexity(self) -> int:
        """Get prep time adjusted for complexity."""
        base_time = self.prep_time
        complexity_multiplier = 1 + (self.complexity_level - 1) * 0.2  # 20% increase per complexity level
        return int(base_time * complexity_multiplier)
    
    @property
    def primary_station(self) -> str:
        """Get the primary cooking station."""
        return self.station_required or "GENERAL"
    
    @property
    def all_stations(self) -> list:
        """Get all stations required for this item."""
        stations = [self.primary_station]
        if self.secondary_stations:
            stations.extend([s.strip() for s in self.secondary_stations.split(",")])
        return list(set(stations))  # Remove duplicates
